

<style>
    .sticky-save-btn {
        position: sticky;bottom: 50%;align-self: flex-end;justify-content: center;
        z-index: 9999 ;background: #246250 ;color: white ;padding: 0.6rem 1rem ;
        border-radius: 8px ;border: none ;box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;display: flex ;
        align-items: center ;gap: 8px !important;font-weight: 500 ;cursor: pointer ;
    }
    .sticky-save-btn:hover:not(:disabled) {
        background: #1e5a47 !important;
    }
    .sticky-save-btn:disabled {
        opacity: 0.6 !important;
        cursor: not-allowed !important;
        background: #246250 !important;
    }
    .sticky-save-btn .loading-spinner {
        display: none;width: 16px;height: 16px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;animation: spin 1s linear infinite;
    }
    .sticky-save-btn.loading .loading-spinner {
        display: block;
    }
    .sticky-save-btn.loading{
        cursor: not-allowed !important;
        pointer-events: none !important;
    }
    .sticky-save-btn.loading .button-icon{
        display: none;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

</style>

<div id="sticky-save-btn" class="sticky-save-btn">
    <div class="loading-spinner"></div>
    <svg class="button-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
    </svg>
    <span class="button-text">{{ $buttonText }}</span>
</div>

<script>
    console.log("Sticky button script loading...");

    document.addEventListener("DOMContentLoaded", function() {
        console.log("DOM loaded, initializing sticky button");

        const stickyBtn = document.getElementById("sticky-save-btn");
        console.log("Sticky button element:", stickyBtn);

        // Find the original submit button - try multiple selectors
        let originalBtn = null;

        // List all buttons for debugging
        const allButtons = document.querySelectorAll("button");
        console.log("All buttons on page:", allButtons.length);
        allButtons.forEach((btn, index) => {
            console.log(`Button ${index}:`, {
                text: btn.textContent.trim(),
                type: btn.type,
                className: btn.className,
                wireClick: btn.getAttribute('wire:click'),
                wireSubmit: btn.getAttribute('wire:submit')
            });
        });

        // Look specifically for the form submit button in the form actions area
        const formActionsArea = document.querySelector('.fi-form-actions');
        if (formActionsArea) {
            console.log("Found form actions area:", formActionsArea);
            originalBtn = formActionsArea.querySelector('button[type="submit"]');
            console.log("Submit button in form actions:", originalBtn);
        }

        // If not found, try the main form
        if (!originalBtn) {
            const mainForm = document.querySelector('form[wire\\:submit]');
            if (mainForm) {
                console.log("Found main form:", mainForm);
                originalBtn = mainForm.querySelector('button[type="submit"]');
                console.log("Submit button in main form:", originalBtn);
            }
        }

        // Last resort - find by text content but be more specific
        if (!originalBtn) {
            allButtons.forEach(btn => {
                const text = btn.textContent.trim().toLowerCase();
                const isInFormActions = btn.closest('.fi-form-actions');
                const isSubmitType = btn.type === 'submit';

                if (isInFormActions && isSubmitType && (text.includes('create') || text.includes('save'))) {
                    originalBtn = btn;
                    console.log("Found button by text content in form actions:", btn);
                }
            });
        }

        console.log("Final selected button:", originalBtn);

        if (!originalBtn) {
            console.log("No original button found, hiding sticky button");
            stickyBtn.style.display = "none";
            return;
        }

        function updateVisibility() {
            const scrollTop = window.pageYOffset;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            // Get original button position
            const originalBtnRect = originalBtn.getBoundingClientRect();
            const originalBtnTop = originalBtnRect.top + scrollTop;

            // Show button when scrolled past original button (with some margin)
            const showBtn = scrollTop > 200; // Show after scrolling 200px
            const nearBottom = scrollTop + windowHeight >= documentHeight - 150; // Hide when 150px from bottom

            console.log("Scroll check:", {
                scrollTop,
                showBtn,
                nearBottom,
                originalBtnTop,
                windowHeight,
                documentHeight
            });

            if (showBtn && !nearBottom) {
                stickyBtn.style.display = "flex";
                console.log("Showing sticky button");
            } else {
                stickyBtn.style.display = "none";
                console.log("Hiding sticky button");
            }
        }

        // For now, keep button always visible for testing
        stickyBtn.style.display = "flex";

        // Watch for changes in the original button state
        function syncButtonState() {
            if (originalBtn.disabled) {
                stickyBtn.disabled = true;
                stickyBtn.classList.add('loading');
            } else {
                stickyBtn.disabled = false;
                stickyBtn.classList.remove('loading');
            }
        }

        // Initial sync
        syncButtonState();

        // Watch for attribute changes on the original button
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' &&
                    (mutation.attributeName === 'disabled' || mutation.attributeName === 'class')) {
                    syncButtonState();
                }
            });
        });

        observer.observe(originalBtn, {
            attributes: true,
            attributeFilter: ['disabled', 'class']
        });

        // Uncomment these lines once save functionality is confirmed working:
        // window.addEventListener("scroll", updateVisibility);
        // updateVisibility();

        stickyBtn.addEventListener("click", function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Prevent multiple clicks
            if (stickyBtn.disabled) {
                console.log("Button already disabled, ignoring click");
                return;
            }

            console.log("Sticky button clicked!");

            if (!originalBtn) {
                console.error("No original button found!");
                alert("Save button not found. Please use the regular save button at the bottom of the page.");
                return;
            }

            // Validate this is actually a save/create button
            const buttonText = originalBtn.textContent.trim().toLowerCase();
            const isInFormActions = originalBtn.closest('.fi-form-actions');
            const isSubmitButton = originalBtn.type === 'submit';

            if (!isInFormActions || !isSubmitButton) {
                console.error("Button validation failed:", {
                    isInFormActions,
                    isSubmitButton,
                    buttonText
                });
                alert("Invalid button detected. Please use the regular save button.");
                return;
            }

            console.log("Button validation passed, triggering click");

            // Show loading state immediately
            stickyBtn.disabled = true;
            stickyBtn.classList.add('loading');

            try {
                // Simple click trigger - safest approach
                originalBtn.click();
                console.log("Button click triggered successfully");

                // The MutationObserver will handle syncing the state from the original button
                // If for some reason the original button doesn't get disabled,
                // we'll reset our state after a timeout
                setTimeout(() => {
                    if (!originalBtn.disabled) {
                        stickyBtn.disabled = false;
                        stickyBtn.classList.remove('loading');
                    }
                }, 100);

            } catch (error) {
                console.error("Error triggering button click:", error);
                // Reset loading state on error
                stickyBtn.disabled = false;
                stickyBtn.classList.remove('loading');
                alert("Error saving. Please use the regular save button at the bottom of the page.");
            }
        });
    });
</script>
