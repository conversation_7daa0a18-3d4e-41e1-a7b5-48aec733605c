<div style="background: red; color: white; padding: 10px; margin: 10px 0;">
    DEBUG: Sticky button view is loading! Button text: {{ $buttonText }}
</div>

<style>
    .sticky-save-btn {
        position: fixed !important;
        right: 24px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        z-index: 9999 !important;
        background: #246250 !important;
        color: white !important;
        padding: 12px 16px !important;
        border-radius: 8px !important;
        border: none !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }
    .sticky-save-btn:hover {
        background: #1e5a47 !important;
    }
    html[dir="rtl"] .sticky-save-btn {
        left: 24px !important;
        right: auto !important;
    }
</style>

<div id="sticky-save-btn" class="sticky-save-btn">
    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
    </svg>
    <span>{{ $buttonText }}</span>
</div>

<script>
    console.log("Sticky button script loading...");

    document.addEventListener("DOMContentLoaded", function() {
        console.log("DOM loaded, initializing sticky button");

        const stickyBtn = document.getElementById("sticky-save-btn");
        console.log("Sticky button element:", stickyBtn);

        // Find the original submit button - try multiple selectors
        let originalBtn = null;

        // List all buttons for debugging
        const allButtons = document.querySelectorAll("button");
        console.log("All buttons on page:", allButtons.length);
        allButtons.forEach((btn, index) => {
            console.log(`Button ${index}:`, {
                text: btn.textContent.trim(),
                type: btn.type,
                className: btn.className,
                wireClick: btn.getAttribute('wire:click'),
                wireSubmit: btn.getAttribute('wire:submit')
            });
        });

        // Try different selectors
        const selectors = [
            "button[type='submit']",
            ".fi-btn[type='submit']",
            "button[wire\\:click*='create']",
            "button[wire\\:click*='save']",
            ".fi-form-actions button",
            "form[wire\\:submit] button[type='submit']",
            "button:contains('Create')",
            "button:contains('Save')"
        ];

        for (let selector of selectors) {
            originalBtn = document.querySelector(selector);
            console.log(`Trying selector "${selector}":`, originalBtn);
            if (originalBtn) break;
        }

        // If still not found, try finding by text content
        if (!originalBtn) {
            allButtons.forEach(btn => {
                const text = btn.textContent.trim().toLowerCase();
                if (text.includes('create') || text.includes('save') || text.includes('submit')) {
                    originalBtn = btn;
                    console.log("Found button by text content:", btn);
                }
            });
        }

        console.log("Final selected button:", originalBtn);

        if (!originalBtn) {
            console.log("No original button found, keeping sticky button visible for testing");
            return;
        }

        function updateVisibility() {
            const scrollTop = window.pageYOffset;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;
            const originalBtnRect = originalBtn.getBoundingClientRect();
            const originalBtnTop = originalBtnRect.top + scrollTop;

            const showBtn = scrollTop > originalBtnTop - windowHeight + 100;
            const nearBottom = scrollTop + windowHeight >= documentHeight - 100;

            console.log("Scroll check:", {
                scrollTop,
                showBtn,
                nearBottom,
                originalBtnTop
            });

            if (showBtn && !nearBottom) {
                stickyBtn.style.display = "flex";
            } else {
                stickyBtn.style.display = "none";
            }
        }

        window.addEventListener("scroll", updateVisibility);
        updateVisibility();

        stickyBtn.addEventListener("click", function(e) {
            e.preventDefault();
            console.log("Sticky button clicked!");
            console.log("Triggering original button:", originalBtn);

            // Try different ways to trigger the button
            if (originalBtn.getAttribute('wire:click')) {
                console.log("Triggering Livewire wire:click");
                // For Livewire buttons, we need to trigger the wire:click
                const wireClick = originalBtn.getAttribute('wire:click');
                if (window.Livewire && window.Livewire.find) {
                    // Try to find the Livewire component and call the method
                    const component = window.Livewire.find(originalBtn.closest('[wire\\:id]')?.getAttribute('wire:id'));
                    if (component && wireClick) {
                        component.call(wireClick);
                        return;
                    }
                }
            }

            // For regular submit buttons or as fallback
            console.log("Triggering regular click");
            originalBtn.click();

            // Also try triggering form submit as backup
            const form = originalBtn.closest('form');
            if (form) {
                console.log("Also triggering form submit");
                form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
            }
        });
    });
</script>
