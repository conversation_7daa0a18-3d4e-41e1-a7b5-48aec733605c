<div style="background: red; color: white; padding: 10px; margin: 10px 0;">
    DEBUG: Sticky button view is loading! Button text: {{ $buttonText }}
</div>

<style>
    .sticky-save-btn {
        position: fixed !important;
        right: 24px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        z-index: 9999 !important;
        background: #246250 !important;
        color: white !important;
        padding: 12px 16px !important;
        border-radius: 8px !important;
        border: none !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }
    .sticky-save-btn:hover {
        background: #1e5a47 !important;
    }
    html[dir="rtl"] .sticky-save-btn {
        left: 24px !important;
        right: auto !important;
    }
</style>

<div id="sticky-save-btn" class="sticky-save-btn">
    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
    </svg>
    <span>{{ $buttonText }}</span>
</div>

<script>
    console.log("Sticky button script loading...");
    
    document.addEventListener("DOMContentLoaded", function() {
        console.log("DOM loaded, initializing sticky button");
        
        const stickyBtn = document.getElementById("sticky-save-btn");
        console.log("Sticky button element:", stickyBtn);
        
        // Find the original submit button
        let originalBtn = document.querySelector("button[type='submit']");
        if (!originalBtn) {
            originalBtn = document.querySelector(".fi-btn[type='submit']");
        }
        if (!originalBtn) {
            originalBtn = document.querySelector("form button");
        }
        
        console.log("Original button found:", originalBtn);
        
        if (!originalBtn) {
            console.log("No original button found, keeping sticky button visible for testing");
            return;
        }
        
        function updateVisibility() {
            const scrollTop = window.pageYOffset;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;
            const originalBtnRect = originalBtn.getBoundingClientRect();
            const originalBtnTop = originalBtnRect.top + scrollTop;
            
            const showBtn = scrollTop > originalBtnTop - windowHeight + 100;
            const nearBottom = scrollTop + windowHeight >= documentHeight - 100;
            
            console.log("Scroll check:", {
                scrollTop,
                showBtn,
                nearBottom,
                originalBtnTop
            });
            
            if (showBtn && !nearBottom) {
                stickyBtn.style.display = "flex";
            } else {
                stickyBtn.style.display = "none";
            }
        }
        
        window.addEventListener("scroll", updateVisibility);
        updateVisibility();
        
        stickyBtn.addEventListener("click", function() {
            console.log("Sticky button clicked!");
            originalBtn.click();
        });
    });
</script>
