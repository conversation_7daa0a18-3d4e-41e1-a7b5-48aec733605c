

<style>
    .sticky-save-btn {
        position: fixed !important;
        right: 24px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        z-index: 9999 !important;
        background: #246250 !important;
        color: white !important;
        padding: 12px 16px !important;
        border-radius: 8px !important;
        border: none !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }
    .sticky-save-btn:hover {
        background: #1e5a47 !important;
    }
    html[dir="rtl"] .sticky-save-btn {
        left: 24px !important;
        right: auto !important;
    }
</style>

<div id="sticky-save-btn" class="sticky-save-btn">
    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
    </svg>
    <span>{{ $buttonText }}</span>
</div>

<script>
    console.log("Sticky button script loading...");

    document.addEventListener("DOMContentLoaded", function() {
        console.log("DOM loaded, initializing sticky button");

        const stickyBtn = document.getElementById("sticky-save-btn");
        console.log("Sticky button element:", stickyBtn);

        // Find the original submit button - try multiple selectors
        let originalBtn = null;

        // List all buttons for debugging
        const allButtons = document.querySelectorAll("button");
        console.log("All buttons on page:", allButtons.length);
        allButtons.forEach((btn, index) => {
            console.log(`Button ${index}:`, {
                text: btn.textContent.trim(),
                type: btn.type,
                className: btn.className,
                wireClick: btn.getAttribute('wire:click'),
                wireSubmit: btn.getAttribute('wire:submit')
            });
        });

        // Look specifically for the form submit button in the form actions area
        const formActionsArea = document.querySelector('.fi-form-actions');
        if (formActionsArea) {
            console.log("Found form actions area:", formActionsArea);
            originalBtn = formActionsArea.querySelector('button[type="submit"]');
            console.log("Submit button in form actions:", originalBtn);
        }

        // If not found, try the main form
        if (!originalBtn) {
            const mainForm = document.querySelector('form[wire\\:submit]');
            if (mainForm) {
                console.log("Found main form:", mainForm);
                originalBtn = mainForm.querySelector('button[type="submit"]');
                console.log("Submit button in main form:", originalBtn);
            }
        }

        // Last resort - find by text content but be more specific
        if (!originalBtn) {
            allButtons.forEach(btn => {
                const text = btn.textContent.trim().toLowerCase();
                const isInFormActions = btn.closest('.fi-form-actions');
                const isSubmitType = btn.type === 'submit';

                if (isInFormActions && isSubmitType && (text.includes('create') || text.includes('save'))) {
                    originalBtn = btn;
                    console.log("Found button by text content in form actions:", btn);
                }
            });
        }

        console.log("Final selected button:", originalBtn);

        if (!originalBtn) {
            console.log("No original button found, hiding sticky button");
            stickyBtn.style.display = "none";
            return;
        }

        function updateVisibility() {
            const scrollTop = window.pageYOffset;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            // Get original button position
            const originalBtnRect = originalBtn.getBoundingClientRect();
            const originalBtnTop = originalBtnRect.top + scrollTop;

            // Show button when scrolled past original button (with some margin)
            const showBtn = scrollTop > 200; // Show after scrolling 200px
            const nearBottom = scrollTop + windowHeight >= documentHeight - 150; // Hide when 150px from bottom

            console.log("Scroll check:", {
                scrollTop,
                showBtn,
                nearBottom,
                originalBtnTop,
                windowHeight,
                documentHeight
            });

            if (showBtn && !nearBottom) {
                stickyBtn.style.display = "flex";
                console.log("Showing sticky button");
            } else {
                stickyBtn.style.display = "none";
                console.log("Hiding sticky button");
            }
        }

        // For now, keep button always visible for testing
        stickyBtn.style.display = "flex";

        // Uncomment these lines once save functionality is confirmed working:
        // window.addEventListener("scroll", updateVisibility);
        // updateVisibility();

        stickyBtn.addEventListener("click", function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log("Sticky button clicked!");
            console.log("Original button details:", {
                element: originalBtn,
                text: originalBtn?.textContent?.trim(),
                type: originalBtn?.type,
                className: originalBtn?.className,
                wireClick: originalBtn?.getAttribute('wire:click'),
                form: originalBtn?.closest('form')
            });

            if (!originalBtn) {
                console.error("No original button found!");
                alert("Save button not found. Please use the regular save button at the bottom of the page.");
                return;
            }

            // Validate this is actually a save/create button
            const buttonText = originalBtn.textContent.trim().toLowerCase();
            const isInFormActions = originalBtn.closest('.fi-form-actions');
            const isSubmitButton = originalBtn.type === 'submit';

            if (!isInFormActions || !isSubmitButton) {
                console.error("Button validation failed:", {
                    isInFormActions,
                    isSubmitButton,
                    buttonText
                });
                alert("Invalid button detected. Please use the regular save button.");
                return;
            }

            console.log("Button validation passed, triggering click");

            try {
                // Simple click trigger - safest approach
                originalBtn.click();
                console.log("Button click triggered successfully");
            } catch (error) {
                console.error("Error triggering button click:", error);
                alert("Error saving. Please use the regular save button at the bottom of the page.");
            }
        });
    });
</script>
