<script>
function initStickyActions() {
    // Remove any existing scroll listeners to prevent duplicates
    if (window.stickyActionsHandler) {
        window.removeEventListener('scroll', window.stickyActionsHandler);
    }
    
    const formActions = document.querySelector('.fi-form-actions');
    
    if (!formActions) {
        console.log('No .fi-form-actions found');
        return;
    }
    
    console.log('Initializing sticky actions for:', formActions);
    
    function handleScroll() {
        const scrollTop = window.pageYOffset;
        const formActionsRect = formActions.getBoundingClientRect();
        const formActionsTop = formActionsRect.top + scrollTop;
        const windowHeight = window.innerHeight;
        
        // Add class when scrolled past the form actions (with some margin)
        const shouldAddClass = scrollTop > formActionsTop - windowHeight + 100;
        
        if (shouldAddClass) {
            formActions.classList.add('sticky-actions');
        } else {
            formActions.classList.remove('sticky-actions');
        }
    }
    
    // Store the handler so we can remove it later
    window.stickyActionsHandler = handleScroll;
    
    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial check
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', initStickyActions);

// Re-initialize on Livewire navigation (SPA mode)
document.addEventListener('livewire:navigated', function() {
    console.log('Livewire navigated, reinitializing sticky actions');
    setTimeout(initStickyActions, 100); // Small delay to ensure DOM is ready
});

// Also listen for Livewire load event (fallback)
document.addEventListener('livewire:load', function() {
    console.log('Livewire loaded, reinitializing sticky actions');
    setTimeout(initStickyActions, 100);
});

// Fallback for older Livewire versions
if (typeof Livewire !== 'undefined') {
    Livewire.hook('component.initialized', () => {
        console.log('Livewire component initialized, reinitializing sticky actions');
        setTimeout(initStickyActions, 100);
    });
}
</script>
