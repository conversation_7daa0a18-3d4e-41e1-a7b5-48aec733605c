<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Shared\Components\CustomPageDeleteAction;
use App\Enums\RoleEnum;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use App\Filament\Resources\RoleResource;
use Filament\Resources\Pages\EditRecord;
use <PERSON>zhanSalleh\FilamentShield\Support\Utils;

class EditRole extends EditRecord
{
    protected static string $resource = RoleResource::class;

    public function getRecordTitle(): string
    {
        return RoleEnum::tryFrom($this->record->name)?->label() ?? $this->record->name;
    }

    public function getTitle(): string
    {
        return __('Edit Role :') . " " . RoleEnum::tryFrom($this->record->name)?->label();
    }

    public Collection $permissions;

    protected function getActions(): array
    {
        return [
            CustomPageDeleteAction::make()
                ->setRelationsList(['users'])
                ->visible(function ($record) {
                    return !in_array($record->name, array_column(RoleEnum::cases(), 'value'));
                }),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        return [
            ...$data,
            'translated_name' => [
                'en' => $data['translated_name']['en'] ?? '',
                'ar' => $data['translated_name']['ar'] ?? '',
            ],
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $this->permissions = collect($data)
            ->filter(function ($permission, $key) {
                return ! in_array($key, ['name', 'guard_name', 'select_all', 'companies', 'translated_name']);
            })
            ->values()
            ->flatten()
            ->unique();

        // Format the translated_name data
        $translatedName = [
            'en' => $data['translated_name']['en'] ?? '',
            'ar' => $data['translated_name']['ar'] ?? '',
        ];

        return [
            'name' => $this->record->name, // Keep the original name
            'guard_name' => $data['guard_name'],
            'companies' => $data['companies'] ?? null,
            'translated_name' => $translatedName,
        ];
    }

    protected function afterSave(): void
    {
        $permissionModels = collect();
        $this->permissions->each(function ($permission) use ($permissionModels) {
            $permissionModels->push(Utils::getPermissionModel()::firstOrCreate([
                'name' => $permission,
                'guard_name' => $this->data['guard_name'],
            ]));
        });

        $this->record->syncPermissions($permissionModels);
    }

    public function getFooter(): ?\Illuminate\Contracts\View\View
    {
        return view('filament.sticky-button', [
            'buttonText' => __('Save Role')
        ]);
    }
}
