<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use <PERSON>zhanSalleh\FilamentShield\Support\Utils;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class CreateRole extends CreateRecord
{
    protected static string $resource = RoleResource::class;

    public Collection $permissions;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $this->permissions = collect($data)
            ->filter(function ($permission, $key) {
                return ! in_array($key, ['name', 'guard_name', 'select_all', 'companies', 'translated_name']);
            })
            ->values()
            ->flatten()
            ->unique();

        // Format the translated_name data
        $translatedName = [
            'en' => $data['translated_name']['en'] ?? '',
            'ar' => $data['translated_name']['ar'] ?? '',
        ];

        // Generate the name slug from English translation
        $name = Str::slug($data['translated_name']['en']);

        return [
            'name' => $name,
            'guard_name' => $data['guard_name'],
            'companies' => $data['companies'] ?? null,
            'translated_name' => $translatedName,
        ];
    }

    protected function afterCreate(): void
    {
        $permissionModels = collect();
        $this->permissions->each(function ($permission) use ($permissionModels) {
            $permissionModels->push(Utils::getPermissionModel()::firstOrCreate([
                /** @phpstan-ignore-next-line */
                'name' => $permission,
                'guard_name' => $this->data['guard_name'],
            ]));
        });

        $this->record->syncPermissions($permissionModels);
    }

    public function getFooter(): ?\Illuminate\Contracts\View\View
    {
        return view('filament.sticky-button', [
            'buttonText' => 'Create'
        ]);
    }
}
